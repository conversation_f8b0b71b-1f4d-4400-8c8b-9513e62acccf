import argparse
import csv
import time
import logging
import praw

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def fetch_subreddit_data(reddit, subreddit_name):
    try:
        logging.info(f"Processing subreddit: {subreddit_name}")
        subreddit = reddit.subreddit(subreddit_name)

        posts = list(subreddit.hot(limit=10))
        if not posts:
            logging.warning(f"No posts found in {subreddit_name}")
            return None

        avg_upvotes = sum(post.score for post in posts) / len(posts)
        timespan = max((time.time() - post.created_utc for post in posts), default=1)

        rules = subreddit.rules()
        min_link_karma = 0
        min_comment_karma = 0
        min_account_age = 0
        for rule in rules:
            rule_text = f"{rule.short_name} {rule.description}".lower()
            if 'karma' in rule_text:
                if 'link' in rule_text:
                    min_link_karma = 1
                if 'comment' in rule_text:
                    min_comment_karma = 1
            if 'account age' in rule_text or 'account must be' in rule_text:
                min_account_age = 1

        logging.info(f"✔ Fetched data for {subreddit_name}")
        return {
            'subreddit': subreddit.display_name,
            'engagement_score': avg_upvotes / timespan,
            'min_link_karma': min_link_karma,
            'min_comment_karma': min_comment_karma,
            'min_account_age': min_account_age,
        }

    except Exception as e:
        logging.error(f"Error with subreddit {subreddit_name}: {e}")
        return None

def calculate_final_score(data, engagement_weight, karma_weight, age_weight):
    barrier_score = (karma_weight * (data['min_link_karma'] + data['min_comment_karma'])) + (age_weight * data['min_account_age'])
    return (engagement_weight * data['engagement_score']) / (barrier_score + 1)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--engagement_weight', type=float, default=1.0)
    parser.add_argument('--karma_weight', type=float, default=1.0)
    parser.add_argument('--age_weight', type=float, default=1.0)
    parser.add_argument('--output', type=str, default='ranked_subreddits.csv')
    args = parser.parse_args()

    reddit = praw.Reddit(
        client_id='5By3t8CS5mX38dsL9oT49g',
        client_secret='e7nfwjDAKCDmIkLarUJfDZcZpJpRVg',
        user_agent='engagement_tracker_script'
    )

    with open("cleaned.txt", "r") as f:
        subreddits = [line.strip() for line in f.readlines() if line.strip()]

    results = []
    for name in subreddits:
        data = fetch_subreddit_data(reddit, name)
        if data:
            data['final_score'] = calculate_final_score(
                data,
                args.engagement_weight,
                args.karma_weight,
                args.age_weight
            )
            results.append(data)
        else:
            logging.warning(f"Skipped subreddit: {name}")

    results.sort(key=lambda x: x['final_score'], reverse=True)

    with open(args.output, 'w', newline='') as csvfile:
        fieldnames = ['subreddit', 'engagement_score', 'min_link_karma', 'min_comment_karma', 'min_account_age', 'final_score']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    logging.info(f"🏁 Ranking saved to {args.output}")

if __name__ == '__main__':
    main()
