import argparse
import csv
import time
import logging
import praw
import random
from prawcore.exceptions import Forbidden, NotFound, TooManyRequests, ServerError

# Import credentials (only need client_id and client_secret for read-only access)
try:
    from reddit_credentials import REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET
except ImportError:
    print("Error: reddit_credentials.py not found!")
    print("Please create reddit_credentials.py with your Reddit credentials.")
    print("For read-only access, you only need REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET.")
    exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def fetch_subreddit_data(reddit, subreddit_name, max_retries=3):
    """Fetch subreddit data according to user specifications:
    - Get 100 newest posts (not hot posts)
    - Get actual poster metrics (account age, karma)
    - Exclude incomplete data rather than using defaults
    """
    for attempt in range(max_retries):
        try:
            logging.info(f"Processing subreddit: {subreddit_name} (attempt {attempt + 1}/{max_retries})")

            # Add random delay to avoid rate limiting
            if attempt > 0:
                delay = random.uniform(2, 5) * attempt
                logging.info(f"Waiting {delay:.1f} seconds before retry...")
                time.sleep(delay)

            subreddit = reddit.subreddit(subreddit_name)

            # Test if subreddit is accessible and get basic info
            try:
                display_name = subreddit.display_name
                subscribers = subreddit.subscribers
                logging.info(f"  Subreddit: {display_name}, Subscribers: {subscribers:,}")
            except Forbidden:
                logging.error(f"Access forbidden to subreddit {subreddit_name} (403 - may be private/quarantined/NSFW restricted)")
                return None
            except NotFound:
                logging.error(f"Subreddit {subreddit_name} not found (404)")
                return None
            except Exception as e:
                logging.error(f"Cannot access basic subreddit info for {subreddit_name}: {e}")
                return None

            # Get 100 NEWEST posts (not hot posts)
            logging.info(f"  Fetching 100 newest posts...")
            try:
                posts = list(subreddit.new(limit=100))
            except Exception as e:
                logging.error(f"Cannot fetch newest posts for {subreddit_name}: {e}")
                return None

            if len(posts) < 50:  # Require at least 50 posts for meaningful data
                logging.warning(f"Insufficient posts in {subreddit_name}: only {len(posts)} posts found (need at least 50)")
                return None

            logging.info(f"  Retrieved {len(posts)} newest posts")

            # Calculate post metrics
            total_upvotes = sum(post.score for post in posts)
            avg_upvotes = total_upvotes / len(posts)

            # Time between newest and oldest post
            newest_post_time = max(post.created_utc for post in posts)
            oldest_post_time = min(post.created_utc for post in posts)
            time_span_hours = (newest_post_time - oldest_post_time) / 3600

            # Get poster metrics (account age and karma)
            logging.info(f"  Analyzing poster metrics...")
            poster_account_ages = []
            poster_link_karma = []
            poster_comment_karma = []
            failed_poster_lookups = 0

            for post in posts:
                try:
                    if post.author is None:  # Deleted account
                        failed_poster_lookups += 1
                        continue

                    author = post.author

                    # Get account age in days
                    account_age_days = (time.time() - author.created_utc) / (24 * 3600)
                    poster_account_ages.append(account_age_days)

                    # Get karma
                    poster_link_karma.append(author.link_karma)
                    poster_comment_karma.append(author.comment_karma)

                except Exception as e:
                    failed_poster_lookups += 1
                    continue

            # Check if we have enough poster data
            successful_lookups = len(poster_account_ages)
            if successful_lookups < len(posts) * 0.7:  # Need at least 70% success rate
                logging.error(f"Insufficient poster data for {subreddit_name}: only {successful_lookups}/{len(posts)} successful lookups ({failed_poster_lookups} failed)")
                return None

            if not poster_account_ages:  # No valid poster data at all
                logging.error(f"No valid poster data found for {subreddit_name}")
                return None

            # Calculate minimum values (as requested)
            min_account_age_days = min(poster_account_ages)
            min_link_karma = min(poster_link_karma)
            min_comment_karma = min(poster_comment_karma)

            # Get subreddit rules
            rules_text = ""
            try:
                rules = list(subreddit.rules)
                rules_text = " | ".join([f"{rule.short_name}: {rule.description}" for rule in rules])
                logging.info(f"  Retrieved {len(rules)} subreddit rules")
            except Exception as e:
                logging.warning(f"Could not fetch rules for {subreddit_name}: {e}")
                rules_text = "Rules unavailable"

            logging.info(f"✔ Successfully processed {subreddit_name}")
            logging.info(f"  Posts: {len(posts)}, Avg upvotes: {avg_upvotes:.1f}, Time span: {time_span_hours:.1f}h")
            logging.info(f"  Min account age: {min_account_age_days:.1f} days, Min karma: {min_link_karma}/{min_comment_karma}")

            return {
                'subreddit_name': display_name,
                'subscribers': subscribers,
                'engagement_score': avg_upvotes / max(time_span_hours, 1),  # Avoid division by zero
                'min_account_age_days': min_account_age_days,
                'min_post_karma': min_link_karma,
                'min_comment_karma': min_comment_karma,
                'avg_upvotes_per_post': avg_upvotes,
                'time_span_hours': time_span_hours,
                'subreddit_rules': rules_text
            }

        except TooManyRequests as e:
            wait_time = 60 * (attempt + 1)  # Exponential backoff
            logging.warning(f"Rate limited for {subreddit_name}. Waiting {wait_time} seconds...")
            time.sleep(wait_time)
            continue

        except ServerError as e:
            logging.warning(f"Server error for {subreddit_name}: {e}. Retrying...")
            time.sleep(5 * (attempt + 1))
            continue

        except Forbidden as e:
            logging.error(f"Access forbidden to subreddit {subreddit_name}: {e}")
            return None

        except NotFound as e:
            logging.error(f"Subreddit {subreddit_name} not found: {e}")
            return None

        except Exception as e:
            logging.error(f"Unexpected error with subreddit {subreddit_name}: {e}")
            if attempt == max_retries - 1:
                return None
            time.sleep(2 * (attempt + 1))
            continue

    logging.error(f"Failed to fetch data for {subreddit_name} after {max_retries} attempts")
    return None

# Removed calculate_final_score function - user wants raw data, not calculated scores

def main():
    parser = argparse.ArgumentParser(description='Reddit Subreddit Data Collector - Newest Posts Analysis')
    parser.add_argument('--output', type=str, default='subreddit_data.csv', help='Output CSV file name')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests in seconds')
    parser.add_argument('--start_from', type=int, default=0, help='Start processing from this line number (0-based)')
    parser.add_argument('--max_subreddits', type=int, default=None, help='Maximum number of subreddits to process')
    args = parser.parse_args()

    # Reddit API setup - using read-only access (no authentication needed)
    reddit = praw.Reddit(
        client_id=REDDIT_CLIENT_ID,
        client_secret=REDDIT_CLIENT_SECRET,
        user_agent='newest_posts_analyzer_v1'
    )

    # Test Reddit connection
    try:
        # Test with a simple subreddit access
        test_subreddit = reddit.subreddit('python')
        _ = test_subreddit.display_name
        logging.info("✓ Reddit connection successful (read-only mode)")
    except Exception as e:
        logging.error(f"Failed to connect to Reddit: {e}")
        return

    with open("cleaned.txt", "r") as f:
        all_subreddits = [line.strip() for line in f.readlines() if line.strip()]

    # Apply start_from and max_subreddits filters
    subreddits = all_subreddits[args.start_from:]
    if args.max_subreddits:
        subreddits = subreddits[:args.max_subreddits]

    logging.info(f"Processing {len(subreddits)} subreddits (starting from line {args.start_from})")
    logging.info(f"Analyzing 100 newest posts per subreddit for poster metrics")

    results = []
    successful = 0
    failed = 0
    skipped_reasons = {}

    for i, name in enumerate(subreddits):
        # Clean subreddit name (remove /r/ prefix if present)
        clean_name = name.lstrip('/r/')

        logging.info(f"Progress: {i+1}/{len(subreddits)} ({((i+1)/len(subreddits)*100):.1f}%)")

        data = fetch_subreddit_data(reddit, clean_name)
        if data:
            results.append(data)
            successful += 1
            logging.info(f"✅ Success: {successful}, Failed: {failed}")
        else:
            failed += 1
            # Track why subreddits are being skipped
            reason = "Unknown error"
            # You could enhance this to track specific failure reasons
            skipped_reasons[clean_name] = reason
            logging.warning(f"❌ Skipped subreddit: {clean_name} (Success: {successful}, Failed: {failed})")

        # Add delay between requests to be respectful to Reddit's API
        if i < len(subreddits) - 1:  # Don't delay after the last request
            time.sleep(args.delay)

    # Sort by engagement score (highest first)
    results.sort(key=lambda x: x['engagement_score'], reverse=True)

    # Write to CSV with the exact columns requested
    with open(args.output, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'subreddit_name',
            'subscribers',
            'engagement_score',
            'min_account_age_days',
            'min_post_karma',
            'min_comment_karma',
            'avg_upvotes_per_post',
            'time_span_hours',
            'subreddit_rules'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    logging.info(f"🏁 Data saved to {args.output}")
    logging.info(f"📊 Final stats: {successful} successful, {failed} failed out of {len(subreddits)} total")

    # Report skipped subreddits and reasons
    if skipped_reasons:
        logging.info(f"📋 Skipped subreddits summary:")
        for subreddit, reason in list(skipped_reasons.items())[:10]:  # Show first 10
            logging.info(f"  {subreddit}: {reason}")
        if len(skipped_reasons) > 10:
            logging.info(f"  ... and {len(skipped_reasons) - 10} more")

    logging.info(f"✨ Analysis complete! Check {args.output} for results.")

if __name__ == '__main__':
    main()
