import argparse
import csv
import time
import logging
import praw
import random
from prawcore.exceptions import Forbidden, NotFound, TooManyRequests, ServerError

# Import credentials (only need client_id and client_secret for read-only access)
try:
    from reddit_credentials import REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET
except ImportError:
    print("Error: reddit_credentials.py not found!")
    print("Please create reddit_credentials.py with your Reddit credentials.")
    print("For read-only access, you only need REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET.")
    exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def fetch_subreddit_data(reddit, subreddit_name, max_retries=3):
    """Fetch subreddit data with improved error handling and retry logic."""
    for attempt in range(max_retries):
        try:
            logging.info(f"Processing subreddit: {subreddit_name} (attempt {attempt + 1}/{max_retries})")

            # Add random delay to avoid rate limiting
            if attempt > 0:
                delay = random.uniform(2, 5) * attempt
                logging.info(f"Waiting {delay:.1f} seconds before retry...")
                time.sleep(delay)

            subreddit = reddit.subreddit(subreddit_name)

            # Test if subreddit is accessible by trying to get basic info
            try:
                _ = subreddit.display_name
                _ = subreddit.subscribers
            except Forbidden:
                logging.error(f"Access forbidden to subreddit {subreddit_name} (403 - may be private/quarantined/NSFW restricted)")
                return None
            except NotFound:
                logging.error(f"Subreddit {subreddit_name} not found (404)")
                return None

            posts = list(subreddit.hot(limit=10))
            if not posts:
                logging.warning(f"No posts found in {subreddit_name}")
                return None

            avg_upvotes = sum(post.score for post in posts) / len(posts)
            timespan = max((time.time() - post.created_utc for post in posts), default=1)

            # Try to get rules, but don't fail if we can't
            min_link_karma = 0
            min_comment_karma = 0
            min_account_age = 0

            try:
                rules = subreddit.rules()
                for rule in rules:
                    rule_text = f"{rule.short_name} {rule.description}".lower()
                    if 'karma' in rule_text:
                        if 'link' in rule_text:
                            min_link_karma = 1
                        if 'comment' in rule_text:
                            min_comment_karma = 1
                    if 'account age' in rule_text or 'account must be' in rule_text:
                        min_account_age = 1
            except (Forbidden, Exception) as e:
                logging.warning(f"Could not fetch rules for {subreddit_name}: {e}")

            logging.info(f"✔ Fetched data for {subreddit_name}")
            return {
                'subreddit': subreddit.display_name,
                'engagement_score': avg_upvotes / timespan,
                'min_link_karma': min_link_karma,
                'min_comment_karma': min_comment_karma,
                'min_account_age': min_account_age,
            }

        except TooManyRequests as e:
            wait_time = 60 * (attempt + 1)  # Exponential backoff
            logging.warning(f"Rate limited for {subreddit_name}. Waiting {wait_time} seconds...")
            time.sleep(wait_time)
            continue

        except ServerError as e:
            logging.warning(f"Server error for {subreddit_name}: {e}. Retrying...")
            time.sleep(5 * (attempt + 1))
            continue

        except Forbidden as e:
            logging.error(f"Access forbidden to subreddit {subreddit_name}: {e}")
            return None

        except NotFound as e:
            logging.error(f"Subreddit {subreddit_name} not found: {e}")
            return None

        except Exception as e:
            logging.error(f"Unexpected error with subreddit {subreddit_name}: {e}")
            if attempt == max_retries - 1:
                return None
            time.sleep(2 * (attempt + 1))
            continue

    logging.error(f"Failed to fetch data for {subreddit_name} after {max_retries} attempts")
    return None

def calculate_final_score(data, engagement_weight, karma_weight, age_weight):
    barrier_score = (karma_weight * (data['min_link_karma'] + data['min_comment_karma'])) + (age_weight * data['min_account_age'])
    return (engagement_weight * data['engagement_score']) / (barrier_score + 1)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--engagement_weight', type=float, default=1.0)
    parser.add_argument('--karma_weight', type=float, default=1.0)
    parser.add_argument('--age_weight', type=float, default=1.0)
    parser.add_argument('--output', type=str, default='ranked_subreddits.csv')
    parser.add_argument('--delay', type=float, default=1.0, help='Delay between requests in seconds')
    parser.add_argument('--start_from', type=int, default=0, help='Start processing from this line number (0-based)')
    parser.add_argument('--max_subreddits', type=int, default=None, help='Maximum number of subreddits to process')
    args = parser.parse_args()

    # Reddit API setup - using read-only access (no authentication needed)
    reddit = praw.Reddit(
        client_id=REDDIT_CLIENT_ID,
        client_secret=REDDIT_CLIENT_SECRET,
        user_agent='engagement_tracker_script_readonly_v4'
    )

    # Test Reddit connection
    try:
        # Test with a simple subreddit access
        test_subreddit = reddit.subreddit('python')
        _ = test_subreddit.display_name
        logging.info("✓ Reddit connection successful (read-only mode)")
    except Exception as e:
        logging.error(f"Failed to connect to Reddit: {e}")
        return

    with open("cleaned.txt", "r") as f:
        all_subreddits = [line.strip() for line in f.readlines() if line.strip()]

    # Apply start_from and max_subreddits filters
    subreddits = all_subreddits[args.start_from:]
    if args.max_subreddits:
        subreddits = subreddits[:args.max_subreddits]

    logging.info(f"Processing {len(subreddits)} subreddits (starting from line {args.start_from})")

    results = []
    successful = 0
    failed = 0

    for i, name in enumerate(subreddits):
        # Clean subreddit name (remove /r/ prefix if present)
        clean_name = name.lstrip('/r/')

        logging.info(f"Progress: {i+1}/{len(subreddits)} ({((i+1)/len(subreddits)*100):.1f}%)")

        data = fetch_subreddit_data(reddit, clean_name)
        if data:
            data['final_score'] = calculate_final_score(
                data,
                args.engagement_weight,
                args.karma_weight,
                args.age_weight
            )
            results.append(data)
            successful += 1
            logging.info(f"✅ Success: {successful}, Failed: {failed}")
        else:
            failed += 1
            logging.warning(f"❌ Skipped subreddit: {clean_name} (Success: {successful}, Failed: {failed})")

        # Add delay between requests to be respectful to Reddit's API
        if i < len(subreddits) - 1:  # Don't delay after the last request
            time.sleep(args.delay)

    results.sort(key=lambda x: x['final_score'], reverse=True)

    with open(args.output, 'w', newline='') as csvfile:
        fieldnames = ['subreddit', 'engagement_score', 'min_link_karma', 'min_comment_karma', 'min_account_age', 'final_score']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    logging.info(f"🏁 Ranking saved to {args.output}")
    logging.info(f"📊 Final stats: {successful} successful, {failed} failed out of {len(subreddits)} total")

if __name__ == '__main__':
    main()
