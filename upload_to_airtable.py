#!/usr/bin/env python3
"""
Upload Reddit subreddit data to Airtable.
"""

import csv
import requests
import json
import logging
from datetime import datetime
import time

# Airtable configuration
AIRTABLE_ACCESS_TOKEN = "**********************************************************************************"
AIRTABLE_BASE_ID = "appN5XyvYsL2ZnwyE"  # Extracted from your URL
AIRTABLE_TABLE_ID = "tbl6Ig7z99bs8MTcJ"  # Extracted from your URL

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def upload_to_airtable(csv_file_path):
    """Upload CSV data to Airtable."""

    logging.info(f"📊 Starting Airtable upload from {csv_file_path}")

    # Read CSV data
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            rows = list(reader)
        logging.info(f"✓ Read {len(rows)} rows from CSV")
    except Exception as e:
        logging.error(f"❌ Failed to read CSV: {e}")
        return False

    # Airtable API endpoint
    url = f"https://api.airtable.com/v0/{AIRTABLE_BASE_ID}/{AIRTABLE_TABLE_ID}"

    # Headers for Airtable API
    headers = {
        "Authorization": f"Bearer {AIRTABLE_ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }

    # Get current timestamp for "Last Check Date"
    current_time = datetime.now().isoformat()

    successful_uploads = 0
    failed_uploads = 0

    # Process each row
    for i, row in enumerate(rows):
        try:
            logging.info(f"📤 Uploading {i+1}/{len(rows)}: {row['subreddit_name']}")

            # Map CSV columns to Airtable fields
            # Members field expects a string (discovered through testing)
            airtable_record = {
                "fields": {
                    "Subreddit": str(row['subreddit_name']),
                    "Members": str(row['subscribers']),  # Keep as string
                    "Last Check Date": current_time,
                    "Engagement Score": round(float(row['engagement_score']), 4),
                    "Min Post Karma": int(row['min_post_karma']),
                    "Min Comment Karma": int(row['min_comment_karma']),
                    "Min Account Age": round(float(row['min_account_age_days']), 2),
                    "Subreddit Rules": str(row['subreddit_rules'])
                }
            }

            # Debug: Log the data being sent
            logging.info(f"  Data: Members={airtable_record['fields']['Members']} (string), Engagement={airtable_record['fields']['Engagement Score']}")

            # Make API request to Airtable
            response = requests.post(url, headers=headers, json=airtable_record)

            if response.status_code == 200:
                logging.info(f"✅ Successfully uploaded: {row['subreddit_name']}")
                successful_uploads += 1
            else:
                logging.error(f"❌ Failed to upload {row['subreddit_name']}: {response.status_code} - {response.text}")
                failed_uploads += 1

            # Add delay to respect Airtable rate limits (5 requests per second)
            time.sleep(0.2)

        except Exception as e:
            logging.error(f"❌ Error processing {row.get('subreddit_name', 'unknown')}: {e}")
            failed_uploads += 1

    # Summary
    logging.info(f"🏁 Upload complete!")
    logging.info(f"📊 Results: {successful_uploads} successful, {failed_uploads} failed")

    if failed_uploads == 0:
        logging.info("🎉 All records uploaded successfully!")
        return True
    else:
        logging.warning(f"⚠️ {failed_uploads} records failed to upload")
        return False

def test_airtable_connection():
    """Test connection to Airtable."""

    logging.info("🔍 Testing Airtable connection...")

    url = f"https://api.airtable.com/v0/{AIRTABLE_BASE_ID}/{AIRTABLE_TABLE_ID}"
    headers = {
        "Authorization": f"Bearer {AIRTABLE_ACCESS_TOKEN}",
    }

    try:
        # Try to get existing records (limit to 1 to test connection)
        response = requests.get(f"{url}?maxRecords=1", headers=headers)

        if response.status_code == 200:
            data = response.json()
            logging.info(f"✅ Airtable connection successful!")
            logging.info(f"📋 Table has {len(data.get('records', []))} existing records (showing max 1)")
            return True
        else:
            logging.error(f"❌ Airtable connection failed: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logging.error(f"❌ Connection test failed: {e}")
        return False

def main():
    print("🚀 AIRTABLE UPLOAD TOOL")
    print("=" * 40)
    print()

    # Test connection first
    if not test_airtable_connection():
        print("❌ Cannot connect to Airtable. Please check:")
        print("1. Access token is correct")
        print("2. Base ID and Table ID are correct")
        print("3. You have write permissions to the table")
        return

    # Upload test data
    csv_file = "test_data.csv"

    print(f"📂 Uploading data from: {csv_file}")
    print("🔄 Starting upload...")
    print()

    success = upload_to_airtable(csv_file)

    if success:
        print()
        print("🎉 SUCCESS! All data uploaded to Airtable!")
        print("🔗 Check your Airtable: https://airtable.com/appN5XyvYsL2ZnwyE/tbl6Ig7z99bs8MTcJ/viwTCE7gOgi219UjR")
        print()
        print("✅ Ready to run the full dataset!")
        print("Next step: python3 RedditSubredditChecker.py --delay 2")
        print("Then: python3 upload_to_airtable.py (with subreddit_data.csv)")
    else:
        print()
        print("❌ Some uploads failed. Check the logs above for details.")

if __name__ == '__main__':
    main()
